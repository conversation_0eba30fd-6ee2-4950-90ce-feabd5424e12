#!/usr/bin/env python3
"""
批量ROI裁剪脚本
从decrypt_videos目录中的所有视频文件中裁剪指定的ROI区域，并保存到decrypt_videos_roi目录
"""

import cv2
import os
import glob
from pathlib import Path
import time

# --- 配置 ---
INPUT_DIR = '/home/<USER>/format_converter_ws/FormatConverter_Hikvision/data/decrypt_videos'
OUTPUT_DIR = '/home/<USER>/format_converter_ws/FormatConverter_Hikvision/data/decrypt_videos_roi'

# ROI参数（从show_roi_params.py获得）
ROI_X = 876
ROI_Y = 418
ROI_WIDTH = 1443
ROI_HEIGHT = 898

def get_video_files(directory):
    """获取目录中的所有视频文件"""
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.flv', '*.wmv']
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(glob.glob(os.path.join(directory, ext)))
    
    return sorted(video_files)

def get_video_info(video_path):
    """获取视频信息"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        return None
    
    # 获取视频属性
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'width': width,
        'height': height
    }

def validate_roi(video_width, video_height, roi_x, roi_y, roi_width, roi_height):
    """验证ROI参数是否在视频范围内"""
    if roi_x < 0 or roi_y < 0:
        return False, "ROI起始坐标不能为负数"
    
    if roi_x + roi_width > video_width:
        return False, f"ROI宽度超出视频范围 (视频宽度: {video_width}, ROI右边界: {roi_x + roi_width})"
    
    if roi_y + roi_height > video_height:
        return False, f"ROI高度超出视频范围 (视频高度: {video_height}, ROI下边界: {roi_y + roi_height})"
    
    return True, "ROI参数有效"

def process_video(input_path, output_path, roi_x, roi_y, roi_width, roi_height):
    """处理单个视频文件，应用ROI裁剪"""
    print(f"正在处理: {os.path.basename(input_path)}")
    
    # 打开输入视频
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {input_path}")
        return False
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"  原始分辨率: {original_width}x{original_height}")
    print(f"  帧率: {fps:.2f} FPS")
    print(f"  总帧数: {frame_count}")
    
    # 验证ROI参数
    is_valid, message = validate_roi(original_width, original_height, roi_x, roi_y, roi_width, roi_height)
    if not is_valid:
        print(f"  错误: {message}")
        cap.release()
        return False
    
    print(f"  ROI区域: ({roi_x}, {roi_y}) 尺寸: {roi_width}x{roi_height}")
    
    # 设置输出视频编码器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (roi_width, roi_height))
    
    if not out.isOpened():
        print(f"错误: 无法创建输出视频文件 {output_path}")
        cap.release()
        return False
    
    # 处理每一帧
    frame_index = 0
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        
        if not ret:
            break
        
        # 裁剪ROI区域
        roi_frame = frame[roi_y:roi_y+roi_height, roi_x:roi_x+roi_width]
        
        # 写入输出视频
        out.write(roi_frame)
        
        frame_index += 1
        
        # 显示进度
        if frame_index % 100 == 0 or frame_index == frame_count:
            progress = (frame_index / frame_count) * 100
            elapsed_time = time.time() - start_time
            if frame_index > 0:
                eta = (elapsed_time / frame_index) * (frame_count - frame_index)
                print(f"  进度: {frame_index}/{frame_count} ({progress:.1f}%) - 预计剩余时间: {eta:.1f}秒")
    
    # 释放资源
    cap.release()
    out.release()
    
    total_time = time.time() - start_time
    print(f"  完成! 处理时间: {total_time:.2f}秒")
    print(f"  输出文件: {output_path}")
    
    return True

def main():
    """主函数"""
    print("=== 批量ROI裁剪工具 ===")
    print(f"输入目录: {INPUT_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    print(f"ROI参数: x={ROI_X}, y={ROI_Y}, width={ROI_WIDTH}, height={ROI_HEIGHT}")
    print()
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 获取所有视频文件
    video_files = get_video_files(INPUT_DIR)
    
    if not video_files:
        print("在输入目录中未找到视频文件!")
        return
    
    print(f"找到 {len(video_files)} 个视频文件:")
    for i, video_file in enumerate(video_files, 1):
        print(f"  {i}. {os.path.basename(video_file)}")
    print()
    
    # 处理每个视频文件
    success_count = 0
    failed_count = 0
    
    for video_file in video_files:
        input_filename = os.path.basename(video_file)
        # 生成输出文件名（保持原文件名）
        name, ext = os.path.splitext(input_filename)
        output_filename = f"{name}{ext}"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        # 检查输出文件是否已存在
        if os.path.exists(output_path):
            print(f"跳过 {input_filename} (输出文件已存在)")
            continue
        
        # 处理视频
        if process_video(video_file, output_path, ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT):
            success_count += 1
        else:
            failed_count += 1
        
        print()  # 空行分隔
    
    # 显示总结
    print("=== 处理完成 ===")
    print(f"成功处理: {success_count} 个文件")
    print(f"处理失败: {failed_count} 个文件")
    print(f"总计: {len(video_files)} 个文件")

if __name__ == "__main__":
    main()
