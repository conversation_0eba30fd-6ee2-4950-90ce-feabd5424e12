import cv2
import os
import glob

# --- 配置 ---
VIDEO_DIR = '/home/<USER>/format_converter_ws/FormatConverter_Hikvision/data/decrypt_videos'
roi = []
cropping = False

def get_video_files():
    """获取视频目录中的所有视频文件"""
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.flv', '*.wmv']
    video_files = []

    for ext in video_extensions:
        video_files.extend(glob.glob(os.path.join(VIDEO_DIR, ext)))

    return sorted(video_files)

def select_video_file(video_files):
    """让用户选择视频文件"""
    if not video_files:
        print("在指定目录中未找到视频文件！")
        return None

    print("找到以下视频文件：")
    for i, video_file in enumerate(video_files):
        filename = os.path.basename(video_file)
        print(f"{i + 1}. {filename}")

    while True:
        try:
            choice = int(input(f"请选择视频文件 (1-{len(video_files)}): ")) - 1
            if 0 <= choice < len(video_files):
                return video_files[choice]
            else:
                print("无效选择，请重新输入。")
        except ValueError:
            print("请输入有效的数字。")

def extract_frame_from_video(video_path, frame_number=0):
    """从视频中提取指定帧"""
    cap = cv2.VideoCapture(video_path)

    if not cap.isOpened():
        print(f"无法打开视频文件: {video_path}")
        return None

    # 设置帧位置
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

    # 读取帧
    ret, frame = cap.read()
    cap.release()

    if ret:
        return frame
    else:
        print(f"无法从视频中提取第 {frame_number} 帧")
        return None

def select_roi(event, x, y, flags, param):
    global roi, cropping

    if event == cv2.EVENT_LBUTTONDOWN:
        roi = [(x, y)]
        cropping = True

    elif event == cv2.EVENT_LBUTTONUP:
        roi.append((x, y))
        cropping = False

        # 绘制最终的矩形并显示参数
        cv2.rectangle(image, roi[0], roi[1], (0, 255, 0), 2)
        cv2.imshow("image", image)

        x1, y1 = roi[0]
        x2, y2 = roi[1]

        # 计算并打印ROI参数
        x_start = min(x1, x2)
        y_start = min(y1, y2)
        width = abs(x2 - x1)
        height = abs(y2 - y1)

        print("--- ROI 参数 ---")
        print(f"x = {x_start}")
        print(f"y = {y_start}")
        print(f"width = {width}")
        print(f"height = {height}")
        print("-----------------")
        print("按任意键退出。")

# 主程序
def main():
    global image

    # 获取视频文件列表
    video_files = get_video_files()

    # 让用户选择视频文件
    selected_video = select_video_file(video_files)
    if selected_video is None:
        return

    print(f"已选择视频: {os.path.basename(selected_video)}")

    # 让用户选择提取哪一帧
    while True:
        try:
            frame_number = int(input("请输入要提取的帧号 (默认为0，即第一帧): ") or "0")
            if frame_number >= 0:
                break
            else:
                print("帧号必须为非负整数。")
        except ValueError:
            print("请输入有效的数字。")

    # 从视频中提取帧
    image = extract_frame_from_video(selected_video, frame_number)
    if image is None:
        return

    print(f"成功提取第 {frame_number} 帧")

    # 创建窗口并设置鼠标回调
    cv2.namedWindow("image")
    cv2.setMouseCallback("image", select_roi)

    print("请在图片上拖动鼠标来选择ROI区域...")
    while True:
        cv2.imshow("image", image)
        key = cv2.waitKey(1) & 0xFF
        if key != 255:  # 如果按下了任意键
            break

    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()