/*
 * FormatConverter_Hikvision - 海康威视解密测试程序
 * 精简版 - 适配精简API接口
 * 
 * 用于测试海康威视IMKH格式的解密功能
 * 基于验证成功的解密流程
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include "decrypt.h"

int main() {
    printf("=== 海康威视解密测试 (精简版) ===\n");
    printf("库版本: v%d.%d.%d\n", 
           (get_library_version() >> 16) & 0xFF,
           (get_library_version() >> 8) & 0xFF,
           get_library_version() & 0xFF);
    
    // 检查输入文件格式
    const char* input_filename = "videos/encrpyted_video_1.mp4";
    int format_check = detect_hikvision_format(input_filename);
    if (format_check == 1) {
        printf("✓ 检测到海康威视IMKH格式\n");
    } else if (format_check == 0) {
        printf("⚠️  警告: 文件可能不是海康威视IMKH格式\n");
    } else {
        printf("✗ 无法检测文件格式\n");
    }
    
    // 打开加密文件
    FILE* input_file = fopen(input_filename, "rb");
    if (!input_file) {
        printf("✗ 无法打开输入文件: %s\n", input_filename);
        printf("请确保文件存在于videos目录中\n");
        return 1;
    }
    
    // 获取文件大小
    fseek(input_file, 0, SEEK_END);
    long file_size = ftell(input_file);
    fseek(input_file, 0, SEEK_SET);
    
    printf("输入文件大小: %ld 字节 (%.2f MB)\n", file_size, file_size / 1024.0 / 1024.0);
    
    // 分配内存缓冲区
    uint8_t* input_data = malloc(file_size);
    uint8_t* output_data = malloc(file_size);
    
    if (!input_data || !output_data) {
        printf("✗ 内存分配失败\n");
        fclose(input_file);
        return 1;
    }
    
    // 读取文件内容
    size_t read_size = fread(input_data, 1, file_size, input_file);
    fclose(input_file);
    
    if (read_size != (size_t)file_size) {
        printf("✗ 文件读取不完整\n");
        free(input_data);
        free(output_data);
        return 1;
    }
    
    printf("✓ 文件读取完成\n");
    
    // 初始化解密上下文
    DecryptContext ctx;
    uint8_t dummy_key[] = {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0};
    
    printf("\n=== 初始化解密上下文 ===\n");
    int result = init_decrypt_context(&ctx, dummy_key, sizeof(dummy_key), ENCRYPT_TYPE_CUSTOM);
    if (result != FC_SUCCESS) {
        printf("✗ 解密上下文初始化失败: %d\n", result);
        free(input_data);
        free(output_data);
        return 1;
    }
    printf("✓ 解密上下文初始化成功\n");
    
    // 执行解密
    printf("\n=== 执行解密 ===\n");
    uint32_t output_size = 0;
    result = decrypt_data(&ctx, input_data, file_size, output_data, &output_size);
    
    printf("解密结果: %s (%d)\n", (result == FC_SUCCESS) ? "成功" : "失败", result);
    printf("输出大小: %u 字节 (%.2f MB)\n", output_size, output_size / 1024.0 / 1024.0);
    
    if (result == FC_SUCCESS) {
        // 保存解密后的原始数据
        FILE* output_file = fopen("hikvision_raw_decrypted.bin", "wb");
        if (output_file) {
            size_t written = fwrite(output_data, 1, output_size, output_file);
            fclose(output_file);
            if (written == output_size) {
                printf("✓ 原始解密数据已保存到: hikvision_raw_decrypted.bin\n");
            } else {
                printf("⚠️  解密数据保存不完整\n");
            }
        } else {
            printf("✗ 无法创建输出文件\n");
        }
        
        // 分析解密后的数据
        printf("\n=== 解密后数据分析 ===\n");
        
        // 检查IMKH头部
        if (output_size >= 4 && memcmp(output_data, "IMKH", 4) == 0) {
            printf("✓ IMKH头部保持完整\n");
        } else {
            printf("✗ IMKH头部损坏\n");
        }
        
        // 查找MPEG-PS包头
        int ps_count = 0;
        for (uint32_t i = 0; i < output_size - 4; i++) {
            if (output_data[i] == 0x00 && output_data[i+1] == 0x00 && 
                output_data[i+2] == 0x01 && output_data[i+3] == 0xBA) {
                ps_count++;
                if (ps_count <= 3) {
                    printf("✓ 发现MPEG-PS包头 #%d 在偏移: 0x%08x\n", ps_count, i);
                }
            }
        }
        printf("总共发现 %d 个MPEG-PS包头\n", ps_count);
        
        // 查找HK标识
        int hk_count = 0;
        for (uint32_t i = 0; i < output_size - 2; i++) {
            if (output_data[i] == 'H' && output_data[i+1] == 'K') {
                hk_count++;
                if (hk_count <= 5) {
                    printf("HK标识 #%d 在偏移: 0x%08x", hk_count, i);
                    if (i + 3 < output_size) {
                        printf(" [类型: 0x%02x, 标志: 0x%02x]", output_data[i+2], output_data[i+3]);
                    }
                    printf("\n");
                }
            }
        }
        printf("总共发现 %d 个HK标识\n", hk_count);
        
        // 显示前128字节的十六进制转储（精简版）
        printf("\n=== 解密后数据头部 (前128字节) ===\n");
        uint32_t dump_size = (output_size > 128) ? 128 : output_size;
        for (uint32_t i = 0; i < dump_size; i += 16) {
            printf("%08x: ", i);
            for (int j = 0; j < 16 && i + j < dump_size; j++) {
                printf("%02x ", output_data[i + j]);
            }
            printf(" |");
            for (int j = 0; j < 16 && i + j < dump_size; j++) {
                char c = output_data[i + j];
                printf("%c", (c >= 32 && c <= 126) ? c : '.');
            }
            printf("|\n");
        }
        
        // 尝试用ffprobe分析解密后的数据
        printf("\n=== 媒体格式验证 ===\n");
        printf("正在使用FFprobe验证解密结果...\n");
        int ffprobe_result = system("ffprobe -v quiet -print_format json -show_format -show_streams hikvision_raw_decrypted.bin 2>/dev/null");
        
        if (ffprobe_result == 0) {
            printf("✓ FFprobe成功识别解密后的媒体文件\n");
            printf("\n=== 媒体信息 ===\n");
            system("ffprobe -v quiet -show_entries format=duration,size,bit_rate -show_entries stream=codec_name,width,height,r_frame_rate hikvision_raw_decrypted.bin 2>/dev/null | grep -E '(duration|size|bit_rate|codec_name|width|height|r_frame_rate)=' | head -10");
        } else {
            printf("✗ FFprobe无法识别解密后的文件\n");
            printf("这可能表示解密过程存在问题\n");
        }
        
        // 自动转换为标准MP4格式
        printf("\n=== 自动转换为标准MP4 ===\n");
        printf("正在转换为标准MP4格式...\n");

        int ffmpeg_result = system("ffmpeg -i hikvision_raw_decrypted.bin -c copy hikvision_decrypted.mp4 -y -v quiet");

        if (ffmpeg_result == 0) {
            printf("✓ MP4转换成功: hikvision_decrypted.mp4\n");

            // 检查输出文件
            FILE* mp4_check = fopen("hikvision_decrypted.mp4", "rb");
            if (mp4_check) {
                fseek(mp4_check, 0, SEEK_END);
                long mp4_size = ftell(mp4_check);
                fclose(mp4_check);
                printf("✓ 输出文件大小: %.2f MB\n", mp4_size / 1024.0 / 1024.0);

                // 验证MP4文件
                printf("正在验证MP4文件...\n");
                int mp4_verify = system("ffprobe -v quiet hikvision_decrypted.mp4 2>/dev/null");
                if (mp4_verify == 0) {
                    printf("✓ MP4文件验证成功\n");

                    // 显示最终文件信息
                    printf("\n=== 最终输出信息 ===\n");
                    system("ffprobe -v quiet -show_entries format=duration,size,bit_rate -show_entries stream=codec_name,width,height,r_frame_rate hikvision_decrypted.mp4 2>/dev/null | grep -E '(duration|size|bit_rate|codec_name|width|height|r_frame_rate)=' | head -8");
                } else {
                    printf("⚠️  MP4文件验证失败\n");
                }
            } else {
                printf("✗ 无法访问输出文件\n");
            }
        } else {
            printf("✗ MP4转换失败\n");
            printf("请手动执行: ffmpeg -i hikvision_raw_decrypted.bin -c copy output.mp4\n");
        }

        // 提供额外操作建议
        printf("\n=== 完成！可选操作 ===\n");
        printf("1. 播放测试:\n");
        printf("   ffplay hikvision_decrypted.mp4\n");
        printf("2. 提取缩略图:\n");
        printf("   ffmpeg -i hikvision_decrypted.mp4 -vframes 1 thumbnail.jpg\n");
        printf("3. 查看详细信息:\n");
        printf("   ffprobe hikvision_decrypted.mp4\n");
        
    } else {
        printf("✗ 解密失败，请检查输入文件格式\n");
    }
    
    // 清理资源
    cleanup_decrypt_context(&ctx);
    free(input_data);
    free(output_data);
    
    printf("\n=== 解密测试完成 ===\n");
    return (result == FC_SUCCESS) ? 0 : 1;
}
