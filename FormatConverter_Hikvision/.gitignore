# 海康威视格式转换器 - Git忽略文件

# ============================================================================
# 视频文件 - 忽略所有视频文件（通常很大且不应版本控制）
# ============================================================================

# MP4 视频文件
*.mp4
*.MP4

# 其他常见视频格式
*.avi
*.AVI
*.mov
*.MOV
*.mkv
*.MKV
*.wmv
*.WMV
*.flv
*.FLV
*.webm
*.WEBM

# 海康威视特有格式
*.imkh
*.IMKH

# ============================================================================
# 编译产物和可执行文件
# ============================================================================

# 目标文件
*.o
*.obj

# 静态库
*.a
*.lib

# 动态库
*.so
*.so.*
*.dll
*.dylib

# 可执行文件
test_decrypt
decrypt_test
format_converter

# 编译生成的库目录
lib/
!lib/.gitkeep

# ============================================================================
# 解密和处理产物
# ============================================================================

# 解密后的原始数据
*.bin
hikvision_raw_decrypted.bin
*_decrypted.*
*_raw.*

# 缩略图
*.jpg
*.jpeg
*.png
thumbnail.*

# ============================================================================
# 日志和报告文件
# ============================================================================

# 日志文件
*.log
batch_decrypt.log
decrypt_log.txt

# 报告文件
*.html
batch_decrypt_report.html

# ============================================================================
# 临时文件和目录
# ============================================================================

# 临时目录
tmp/
temp/
.tmp/

# 系统临时文件
*~
.DS_Store
Thumbs.db

# 编辑器临时文件
*.swp
*.swo
*~
.vscode/
.idea/

# ============================================================================
# 数据目录（通常包含大文件）
# ============================================================================

# 加密视频目录
data/encrypt_videos/
data/decrypt_videos/
videos/

# 但保留目录结构
!data/.gitkeep
!videos/.gitkeep

# ============================================================================
# 配置文件（可能包含敏感信息）
# ============================================================================

# 用户自定义配置
*_local.conf
*_private.conf
.env
.env.local

# 但保留示例配置文件
!*_example.conf
!*_template.conf

# ============================================================================
# 压缩包和发布文件
# ============================================================================

# 压缩包
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.rar
*.7z

# 发布包
FormatConverter_Hikvision_*.tar.gz

# ============================================================================
# 开发和调试文件
# ============================================================================

# 调试信息
*.dSYM/
*.pdb

# 性能分析文件
*.prof
gmon.out

# 内存检查报告
valgrind-*.log

# ============================================================================
# 系统和IDE文件
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# IDE
.vscode/
.idea/
*.sublime-*

# ============================================================================
# 备份文件
# ============================================================================

# 自动备份
*.bak
*.backup
*.old

# 版本备份
*_v[0-9]*
*_backup_*

# ============================================================================
# 测试相关
# ============================================================================

# 测试输出
test_output/
test_results/

# 覆盖率报告
*.gcov
*.gcda
*.gcno
coverage/

# ============================================================================
# 文档生成
# ============================================================================

# Doxygen输出
docs/html/
docs/latex/
docs/man/

# ============================================================================
# 特殊标记文件
# ============================================================================

# 保留重要的空目录
!.gitkeep
!.keep
