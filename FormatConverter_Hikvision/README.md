# FormatConverter_Hikvision - Hikvision Encrypted MP4 Decryptor

A streamlined decryption tool specifically designed for Hikvision IMKH format encrypted videos.

## 🎯 Features

- ✅ **Core Functionality** - Focused on Hikvision decryption essentials
- ✅ **Proven Algorithm** - Based on successful decryption cases
- ✅ **Lightweight** - 60% less code than full version
- ✅ **Fast Compilation** - Builds in under 10 seconds
- ✅ **Batch Processing** - Process multiple files automatically

## 📊 Supported Formats

**Input:**
- Hikvision IMKH encrypted MP4 files
- Dual IMKH header structure
- H.265/HEVC video encoding
- Various resolutions (2560x1440, etc.)

**Output:**
- Standard MP4 files
- Compatible with all major players
- Original video quality preserved

## 🚀 Quick Start

### Single File Processing

```bash
# 1. Compile the project
make

# 2. Decrypt a single file
./decrypt_video.sh your_encrypted_video.mp4

# 3. Specify output filename
./decrypt_video.sh input.mp4 output.mp4
```

### Batch Processing (Recommended)

```bash
# 1. Prepare directory structure
mkdir -p data/encrypt_videos data/decrypt_videos

# 2. Copy encrypted videos to input directory
cp /path/to/encrypted/*.mp4 data/encrypt_videos/

# 3. Run basic batch processing
./batch_decrypt.sh

# 4. Run advanced batch processing with configuration
./batch_decrypt_advanced.sh -v
```

## 📋 System Requirements

- **OS**: Linux (Ubuntu 18.04+ recommended)
- **Compiler**: GCC 4.8+
- **Tools**: Make, FFmpeg
- **Memory**: At least 200MB available

## 🔧 Project Structure

```
FormatConverter_Hikvision/
├── src/
│   └── decrypt.c              # Core decryption implementation
├── include/
│   └── decrypt.h              # API interface
├── data/
│   ├── encrypt_videos/        # Input directory for encrypted videos
│   └── decrypt_videos/        # Output directory for decrypted videos
├── decrypt_video.sh           # Single file decryption script
├── batch_decrypt.sh           # Basic batch processing script
├── batch_decrypt_advanced.sh  # Advanced batch processing script
├── batch_config.conf          # Batch processing configuration
└── Makefile                   # Build configuration
```

## 📦 Batch Processing Guide

### Basic Batch Script (batch_decrypt.sh)

Simple batch processing with basic parallel processing and error handling.

```bash
# View help
./batch_decrypt.sh -h

# Basic usage with default settings
./batch_decrypt.sh

# Custom parameters
./batch_decrypt.sh -i input_dir -o output_dir -p 4 -f -v
```

**Parameters:**
- `-i, --input DIR`: Input directory (default: data/encrypt_videos)
- `-o, --output DIR`: Output directory (default: data/decrypt_videos)
- `-p, --parallel N`: Number of parallel processes (default: 2)
- `-f, --force`: Force overwrite existing files
- `-v, --verbose`: Verbose output mode

### Advanced Batch Script (batch_decrypt_advanced.sh)

Enterprise-grade batch processing with configuration files, logging, reporting, and notifications.

**Key Features:**
- 📋 Configuration file support
- 📊 Detailed logging and error tracking
- 📈 HTML report generation
- 🔔 Multiple notification methods (desktop, email, webhook)
- 🔄 Automatic retry mechanism
- 💾 Disk space checking
- 🎯 File filtering (size, pattern)
- 🏗️ Directory structure preservation

```bash
# Use default configuration
./batch_decrypt_advanced.sh

# Use custom configuration file
./batch_decrypt_advanced.sh -c my_config.conf

# Override specific parameters
./batch_decrypt_advanced.sh -i /custom/input -o /custom/output -p 8 -v
```

## ⚙️ Configuration File

The `batch_config.conf` file contains all adjustable parameters:

```bash
# Directory settings
INPUT_DIR="data/encrypt_videos"
OUTPUT_DIR="data/decrypt_videos"

# Processing settings
PARALLEL_COUNT=2
FORCE_OVERWRITE=false
VERBOSE=false

# File filtering
SUPPORTED_EXTENSIONS="mp4 MP4"
MIN_FILE_SIZE=1024
MAX_FILE_SIZE=0

# Error handling
ERROR_BEHAVIOR="continue"
MAX_RETRIES=1
RETRY_INTERVAL=5

# Logging
ENABLE_LOGGING=true
LOG_FILE="batch_decrypt.log"
LOG_RETENTION_DAYS=7

# Notifications
ENABLE_NOTIFICATION=false
NOTIFICATION_METHOD="desktop"
```

## 💡 Usage Examples

### Example 1: Basic Batch Processing
```bash
# Prepare files
mkdir -p data/encrypt_videos data/decrypt_videos
cp /path/to/encrypted/*.mp4 data/encrypt_videos/

# Compile and process
make
./batch_decrypt.sh

# Check results
ls -la data/decrypt_videos/
```

### Example 2: Large Scale Processing
```bash
# Configure processing parameters
cp batch_config.conf my_config.conf
# Edit my_config.conf to set appropriate parallel count and other parameters

# Execute processing
./batch_decrypt_advanced.sh -c my_config.conf -v

# View processing report
firefox batch_decrypt_report.html
```

### Example 3: Custom Directory Structure
```bash
./batch_decrypt_advanced.sh \
  -i /media/hikvision_videos \
  -o /media/decrypted_videos \
  -p 4 \
  -f \
  -v
```

## 🛠️ Build Targets

```bash
make           # Compile all
make test      # Compile and run test
make clean     # Clean build artifacts
make info      # Show library information
make help      # Show help information
```

## 📈 Performance Metrics

| Metric | Value |
|--------|-------|
| Library size | ~20KB |
| Compile time | <10 seconds |
| Decryption speed | 76MB/0.05s |
| Memory usage | <200MB |
| Code lines | ~300 lines |

## 🔍 Best Practices

### Performance Optimization
1. **Parallel Count**: Set to 50-75% of CPU cores
2. **Disk Space**: Ensure sufficient space in output directory
3. **Memory Monitoring**: Watch memory usage for large files

### Error Handling
1. **Retry Mechanism**: Configure retries for unstable environments
2. **Log Analysis**: Check logs for troubleshooting

### File Organization
- Use consistent naming conventions
- Maintain directory structure
- Monitor processing progress

## 🔍 Troubleshooting

### Common Issues

**Q: Compilation fails**
```bash
# Solution
make clean && make
```

**Q: Test file not found**
```bash
# Solution
cp your_video.mp4 videos/encrpyted_video_1.mp4
```

**Q: FFmpeg conversion fails**
```bash
# Solution
ffprobe hikvision_raw_decrypted.bin  # Verify decryption result
```

**Q: Batch processing fails**
```bash
# Check logs
tail -f batch_decrypt.log

# Check disk space
df -h data/decrypt_videos

# Verify file permissions
ls -la data/encrypt_videos/
```

## ⚠️ Important Notes

### Legal Disclaimer
- Use only with legally owned video files
- Not for breaking copyright protection
- User assumes all risks

### Technical Limitations
- Specifically designed for Hikvision IMKH format
- Requires FFmpeg for final format conversion
- Primarily tested on Linux environments

## 🤝 Contributing

Issues and Pull Requests are welcome!

## 📄 License

MIT License

---

**Version**: v2.0.0 - Hikvision Edition  
**Last Updated**: 2025-07-25  
**Status**: ✅ Tested and Verified
