# FormatConverter_Hikvision - 精简版Makefile
# 专门用于海康威视IMKH格式解密的优化构建配置

# ============================================================================
# 编译器和标志设置
# ============================================================================

CC = gcc
CFLAGS = -Wall -Wextra -O2 -fPIC -std=c99
LDFLAGS = -shared
INCLUDES = -I./include

# 调试版本标志
DEBUG ?= 0
ifeq ($(DEBUG), 1)
    CFLAGS += -g -DDEBUG -O0
    LDFLAGS += -g
endif

# ============================================================================
# 目录和文件定义
# ============================================================================

# 目录结构
SRC_DIR = src
INCLUDE_DIR = include
LIB_DIR = lib
VIDEOS_DIR = videos

# 目标文件
LIB_NAME = libhikvision_decrypt
LIB_TARGET = $(LIB_DIR)/$(LIB_NAME).so
TEST_TARGET = test_decrypt

# 源文件
LIB_SOURCES = $(SRC_DIR)/decrypt.c
TEST_SOURCES = test_decrypt.c

# 对象文件
LIB_OBJECTS = $(LIB_SOURCES:.c=.o)

# ============================================================================
# 构建目标
# ============================================================================

# 默认目标
all: $(LIB_TARGET) $(TEST_TARGET)

# 创建动态库
$(LIB_TARGET): $(LIB_OBJECTS) | $(LIB_DIR)
	@echo "正在创建动态库: $@"
	$(CC) $(LDFLAGS) -o $@ $^
	@echo "✓ 动态库创建完成"

# 编译库源文件
$(SRC_DIR)/%.o: $(SRC_DIR)/%.c
	@echo "正在编译: $<"
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 创建测试程序
$(TEST_TARGET): $(TEST_SOURCES) $(LIB_TARGET)
	@echo "正在创建测试程序: $@"
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $< -L$(LIB_DIR) -lhikvision_decrypt -Wl,-rpath,./$(LIB_DIR)
	@echo "✓ 测试程序创建完成"

# 创建必要的目录
$(LIB_DIR):
	@mkdir -p $(LIB_DIR)

$(VIDEOS_DIR):
	@mkdir -p $(VIDEOS_DIR)

# ============================================================================
# 实用目标
# ============================================================================

# 运行测试
test: $(TEST_TARGET) | $(VIDEOS_DIR)
	@echo "=== 运行解密测试 ==="
	@if [ ! -f "$(VIDEOS_DIR)/encrpyted_video_1.mp4" ]; then \
		echo "⚠️  警告: 测试视频文件不存在"; \
		echo "请将海康威视加密视频文件复制到 $(VIDEOS_DIR)/encrpyted_video_1.mp4"; \
		echo "或者修改test_decrypt.c中的文件路径"; \
	else \
		export LD_LIBRARY_PATH=./$(LIB_DIR):$$LD_LIBRARY_PATH && ./$(TEST_TARGET); \
	fi

# 快速测试（假设文件已存在）
run: $(TEST_TARGET)
	@export LD_LIBRARY_PATH=./$(LIB_DIR):$$LD_LIBRARY_PATH && ./$(TEST_TARGET)

# 一键解密（完整流程：解密+转换MP4）
decrypt: $(TEST_TARGET) | $(VIDEOS_DIR)
	@echo "=== 一键解密海康威视视频 ==="
	@if [ ! -f "$(VIDEOS_DIR)/encrpyted_video_1.mp4" ]; then \
		echo "⚠️  请将海康威视加密视频文件复制到 $(VIDEOS_DIR)/encrpyted_video_1.mp4"; \
		echo "或者使用: ./decrypt_video.sh your_video.mp4"; \
	else \
		export LD_LIBRARY_PATH=./$(LIB_DIR):$$LD_LIBRARY_PATH && ./$(TEST_TARGET); \
		echo "✓ 完整解密流程完成！输出文件: hikvision_decrypted.mp4"; \
	fi

# 安装到系统（可选）
install: $(LIB_TARGET)
	@echo "正在安装到系统..."
	sudo cp $(LIB_TARGET) /usr/local/lib/
	sudo cp $(INCLUDE_DIR)/decrypt.h /usr/local/include/
	sudo ldconfig
	@echo "✓ 安装完成"

# 卸载
uninstall:
	@echo "正在从系统卸载..."
	sudo rm -f /usr/local/lib/$(LIB_NAME).so
	sudo rm -f /usr/local/include/decrypt.h
	sudo ldconfig
	@echo "✓ 卸载完成"

# ============================================================================
# 清理和维护
# ============================================================================

# 清理编译产物
clean:
	@echo "正在清理编译产物..."
	rm -f $(LIB_OBJECTS)
	rm -f $(TEST_TARGET)
	rm -f *.o
	@echo "✓ 清理完成"

# 深度清理（包括库文件）
distclean: clean
	@echo "正在深度清理..."
	rm -rf $(LIB_DIR)
	rm -f hikvision_raw_decrypted.bin
	rm -f *.mp4
	rm -f *.jpg
	@echo "✓ 深度清理完成"

# 重新构建
rebuild: clean all

# ============================================================================
# 开发辅助目标
# ============================================================================

# 检查代码风格
check:
	@echo "正在检查代码风格..."
	@which cppcheck > /dev/null 2>&1 && cppcheck --enable=all --std=c99 $(SRC_DIR)/ $(TEST_SOURCES) || echo "cppcheck未安装，跳过检查"

# 显示库信息
info: $(LIB_TARGET)
	@echo "=== 库信息 ==="
	@echo "库文件: $(LIB_TARGET)"
	@ls -lh $(LIB_TARGET)
	@echo "导出符号:"
	@nm -D $(LIB_TARGET) | grep " T " | head -10
	@echo "依赖库:"
	@ldd $(LIB_TARGET)

# 创建发布包
package: clean
	@echo "正在创建发布包..."
	@tar -czf FormatConverter_Hikvision_$(shell date +%Y%m%d).tar.gz \
		--exclude='*.tar.gz' \
		--exclude='.git*' \
		--exclude='videos/*.mp4' \
		--exclude='*.bin' \
		.
	@echo "✓ 发布包创建完成"

# ============================================================================
# 帮助信息
# ============================================================================

help:
	@echo "FormatConverter_Hikvision - 海康威视解密工具"
	@echo ""
	@echo "可用目标:"
	@echo "  all        - 编译库和测试程序 (默认)"
	@echo "  test       - 编译并运行测试"
	@echo "  run        - 运行测试程序"
	@echo "  decrypt    - 一键解密（完整流程）"
	@echo "  clean      - 清理编译产物"
	@echo "  distclean  - 深度清理"
	@echo "  rebuild    - 重新构建"
	@echo "  install    - 安装到系统"
	@echo "  uninstall  - 从系统卸载"
	@echo "  check      - 代码风格检查"
	@echo "  info       - 显示库信息"
	@echo "  package    - 创建发布包"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "编译选项:"
	@echo "  DEBUG=1    - 编译调试版本"
	@echo ""
	@echo "使用示例:"
	@echo "  make                    # 编译所有"
	@echo "  make test              # 运行测试"
	@echo "  make DEBUG=1           # 编译调试版本"
	@echo "  make clean && make     # 重新编译"

# ============================================================================
# 特殊目标声明
# ============================================================================

.PHONY: all test run install uninstall clean distclean rebuild check info package help
.DEFAULT_GOAL := all

# 保持中间文件
.PRECIOUS: $(LIB_OBJECTS)
