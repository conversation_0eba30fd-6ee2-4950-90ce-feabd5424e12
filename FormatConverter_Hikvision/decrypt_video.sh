#!/bin/bash

# 海康威视视频一键解密脚本
# 完整流程：解密 -> 转换MP4 -> 验证

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "海康威视视频一键解密工具"
    echo ""
    echo "使用方法:"
    echo "  $0 <输入文件> [输出文件]"
    echo ""
    echo "示例:"
    echo "  $0 hikvision_video.mp4"
    echo "  $0 hikvision_video.mp4 my_output.mp4"
    echo ""
    echo "功能:"
    echo "  - 自动检测海康威视IMKH格式"
    echo "  - 执行解密处理"
    echo "  - 转换为标准MP4格式"
    echo "  - 验证输出文件"
    echo ""
}

# 检查参数
if [ $# -lt 1 ]; then
    show_usage
    exit 1
fi

INPUT_FILE="$1"
OUTPUT_FILE="${2:-$(basename "$INPUT_FILE" .mp4)_decrypted.mp4}"

print_info "=== 海康威视视频一键解密工具 ==="
print_info "输入文件: $INPUT_FILE"
print_info "输出文件: $OUTPUT_FILE"

# 检查输入文件
if [ ! -f "$INPUT_FILE" ]; then
    print_error "输入文件不存在: $INPUT_FILE"
    exit 1
fi

# 检查依赖
print_info "检查系统依赖..."

if [ ! -f "./test_decrypt" ]; then
    print_error "解密程序不存在，请先编译:"
    echo "  make"
    exit 1
fi

if [ ! -f "./lib/libhikvision_decrypt.so" ]; then
    print_error "解密库不存在，请先编译:"
    echo "  make"
    exit 1
fi

if ! command -v ffmpeg &> /dev/null; then
    print_error "FFmpeg未安装，请安装:"
    echo "  sudo apt install ffmpeg"
    exit 1
fi

print_success "依赖检查完成"

# 准备输入文件
print_info "准备解密环境..."
if [ "$INPUT_FILE" != "videos/encrpyted_video_1.mp4" ]; then
    cp "$INPUT_FILE" videos/encrpyted_video_1.mp4
    print_success "输入文件准备完成"
else
    print_info "使用现有测试文件"
fi

# 执行解密
print_info "开始解密处理..."
if ./test_decrypt > decrypt_log.txt 2>&1; then
    print_success "解密处理完成"
    
    # 检查解密结果
    if [ -f "hikvision_decrypted.mp4" ]; then
        print_success "MP4转换完成"
        
        # 移动到指定输出位置
        if [ "$OUTPUT_FILE" != "hikvision_decrypted.mp4" ]; then
            mv hikvision_decrypted.mp4 "$OUTPUT_FILE"
            print_info "文件已保存为: $OUTPUT_FILE"
        fi
        
        # 显示文件信息
        print_info "输出文件信息:"
        ls -lh "$OUTPUT_FILE"
        
        # 验证文件
        print_info "验证输出文件..."
        if ffprobe -v quiet "$OUTPUT_FILE" 2>/dev/null; then
            print_success "文件验证成功"
            
            # 显示媒体信息
            echo ""
            print_info "媒体信息:"
            ffprobe -v quiet -show_entries format=duration,size,bit_rate -show_entries stream=codec_name,width,height,r_frame_rate "$OUTPUT_FILE" 2>/dev/null | grep -E '(duration|size|bit_rate|codec_name|width|height|r_frame_rate)=' | while read line; do
                echo "  $line"
            done
            
        else
            print_warning "文件验证失败，但文件已生成"
        fi
        
        # 清理临时文件
        rm -f hikvision_raw_decrypted.bin
        rm -f videos/encrpyted_video_1.mp4
        
        print_success "=== 解密完成！==="
        print_info "输出文件: $OUTPUT_FILE"
        print_info "可以使用任何播放器播放此文件"
        
        # 提供播放建议
        echo ""
        print_info "播放测试:"
        echo "  ffplay \"$OUTPUT_FILE\""
        echo "  vlc \"$OUTPUT_FILE\""
        
    else
        print_error "MP4转换失败"
        if [ -f "hikvision_raw_decrypted.bin" ]; then
            print_info "原始解密数据已生成，可手动转换:"
            echo "  ffmpeg -i hikvision_raw_decrypted.bin -c copy \"$OUTPUT_FILE\""
        fi
        exit 1
    fi
    
else
    print_error "解密失败"
    print_info "查看详细日志:"
    echo "  cat decrypt_log.txt"
    exit 1
fi
