/*
 * FormatConverter_Hikvision - 海康威视加密MP4解密器
 * 精简版API - 仅包含核心解密功能
 * 
 * 基于深度逆向分析的海康威视IMKH格式解密工具
 * 专门用于将海康威视监控设备的加密MP4文件转换为标准格式
 */

#ifndef DECRYPT_H
#define DECRYPT_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// 常量定义
// ============================================================================

// 返回码定义
#define FC_SUCCESS                  0
#define FC_ERROR_INVALID_PARAM     -1
#define FC_ERROR_DECRYPT_FAILED    -4
#define FC_ERROR_OUT_OF_MEMORY     -7

// 加密类型定义
#define ENCRYPT_TYPE_NONE          0
#define ENCRYPT_TYPE_CUSTOM        99

// 缓冲区大小
#define FC_MAX_KEY_SIZE            256

// ============================================================================
// 核心数据结构
// ============================================================================

/**
 * 解密密钥结构体
 * 用于存储解密所需的密钥信息
 */
typedef struct {
    uint32_t key_length;                    // 密钥长度
    uint8_t  key_data[FC_MAX_KEY_SIZE];     // 密钥数据
    uint32_t key_type;                      // 密钥类型
    uint32_t flags;                         // 标志位
} DecryptKey;

/**
 * 解密上下文结构体
 * 包含解密过程中需要的所有状态信息
 */
typedef struct {
    DecryptKey key;                         // 解密密钥
    uint32_t   algorithm;                   // 解密算法类型
    uint32_t   block_size;                  // 数据块大小
    uint8_t    iv[16];                      // 初始化向量
    uint32_t   initialized;                 // 初始化标志
} DecryptContext;

// ============================================================================
// 核心API函数声明
// ============================================================================

/**
 * 初始化解密上下文
 * 
 * @param ctx 解密上下文指针
 * @param key_data 密钥数据
 * @param key_length 密钥长度
 * @param algorithm 解密算法类型
 * @return FC_SUCCESS 成功，其他值表示错误
 */
int init_decrypt_context(DecryptContext* ctx, 
                        const uint8_t* key_data, 
                        uint32_t key_length, 
                        uint32_t algorithm);

/**
 * 解密数据
 * 核心解密函数，支持海康威视IMKH格式
 * 
 * @param ctx 解密上下文
 * @param input 输入数据缓冲区
 * @param input_size 输入数据大小
 * @param output 输出数据缓冲区
 * @param output_size 输出数据大小指针
 * @return FC_SUCCESS 成功，其他值表示错误
 */
int decrypt_data(const DecryptContext* ctx, 
                const uint8_t* input, 
                uint32_t input_size, 
                uint8_t* output, 
                uint32_t* output_size);

/**
 * 清理解密上下文
 * 释放资源并清零敏感数据
 * 
 * @param ctx 解密上下文指针
 */
void cleanup_decrypt_context(DecryptContext* ctx);

// ============================================================================
// 工具函数声明
// ============================================================================

/**
 * 检测文件是否为海康威视IMKH格式
 * 
 * @param file_path 文件路径
 * @return 1 是IMKH格式，0 不是，-1 错误
 */
int detect_hikvision_format(const char* file_path);

/**
 * 获取库版本信息
 * 
 * @return 版本号 (格式: 0xMMmmpp, MM=主版本, mm=次版本, pp=补丁版本)
 */
uint32_t get_library_version(void);

#ifdef __cplusplus
}
#endif

#endif // DECRYPT_H
