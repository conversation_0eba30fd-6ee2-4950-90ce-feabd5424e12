# 海康威视视频批量解密配置文件
# 
# 此文件包含批量处理脚本的默认配置
# 可以通过修改这些值来自定义处理行为

# ============================================================================
# 目录配置
# ============================================================================

# 输入目录 - 存放加密视频文件的目录
INPUT_DIR="data/encrypt_videos"

# 输出目录 - 存放解密后视频文件的目录
OUTPUT_DIR="data/decrypt_videos"

# ============================================================================
# 处理配置
# ============================================================================

# 并行处理数量 - 同时处理的文件数量
# 建议设置为CPU核心数的一半，避免系统过载
PARALLEL_COUNT=2

# 强制覆盖已存在的文件
# true: 覆盖已存在的解密文件
# false: 跳过已存在的解密文件
FORCE_OVERWRITE=false

# 详细输出模式
# true: 显示详细的处理信息
# false: 只显示关键信息
VERBOSE=false

# ============================================================================
# 文件过滤配置
# ============================================================================

# 支持的文件扩展名 (用空格分隔)
SUPPORTED_EXTENSIONS="mp4 MP4"

# 文件名模式过滤 (可选，留空表示处理所有文件)
# 例如: "hikvision_*" 只处理以hikvision_开头的文件
FILE_PATTERN=""

# 最小文件大小 (字节) - 跳过小于此大小的文件
MIN_FILE_SIZE=1024

# 最大文件大小 (字节) - 跳过大于此大小的文件 (0表示无限制)
MAX_FILE_SIZE=0

# ============================================================================
# 输出配置
# ============================================================================

# 输出文件名后缀
OUTPUT_SUFFIX="_decrypted"

# 保持原始目录结构
# true: 在输出目录中保持与输入目录相同的子目录结构
# false: 将所有文件输出到输出目录的根目录
PRESERVE_STRUCTURE=true

# ============================================================================
# 错误处理配置
# ============================================================================

# 遇到错误时的行为
# continue: 继续处理其他文件
# stop: 遇到错误立即停止
ERROR_BEHAVIOR="continue"

# 最大重试次数
MAX_RETRIES=1

# 重试间隔 (秒)
RETRY_INTERVAL=5

# ============================================================================
# 日志配置
# ============================================================================

# 启用日志记录
ENABLE_LOGGING=true

# 日志文件路径
LOG_FILE="batch_decrypt.log"

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL="INFO"

# 保留日志天数
LOG_RETENTION_DAYS=7

# ============================================================================
# 性能配置
# ============================================================================

# 临时目录 (留空使用系统默认)
TEMP_DIR=""

# 内存限制 (MB) - 单个进程的内存使用限制
MEMORY_LIMIT=1024

# 磁盘空间检查
# true: 处理前检查输出目录的可用空间
# false: 不检查磁盘空间
CHECK_DISK_SPACE=true

# 最小可用空间 (MB) - 如果可用空间少于此值则停止处理
MIN_FREE_SPACE=1024

# ============================================================================
# 通知配置
# ============================================================================

# 启用完成通知
ENABLE_NOTIFICATION=false

# 通知方式 (email, webhook, desktop)
NOTIFICATION_METHOD="desktop"

# 邮件通知配置 (如果启用邮件通知)
EMAIL_TO=""
EMAIL_SUBJECT="海康威视视频批量解密完成"

# Webhook通知配置 (如果启用webhook通知)
WEBHOOK_URL=""

# ============================================================================
# 高级配置
# ============================================================================

# 预处理钩子脚本 (在处理每个文件前执行)
PRE_PROCESS_HOOK=""

# 后处理钩子脚本 (在处理每个文件后执行)
POST_PROCESS_HOOK=""

# 自定义FFmpeg参数
FFMPEG_PARAMS="-c copy"

# 验证输出文件
VERIFY_OUTPUT=true

# 生成处理报告
GENERATE_REPORT=true

# 报告文件路径
REPORT_FILE="batch_decrypt_report.html"
