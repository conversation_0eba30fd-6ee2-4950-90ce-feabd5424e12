#!/bin/bash

# 海康威视视频批量解密脚本 - 增强版
# 支持配置文件、日志记录、错误处理、进度监控等高级功能

set -e  # 遇到错误立即退出

# ============================================================================
# 全局变量和配置
# ============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/batch_config.conf"
LOG_FILE=""
TEMP_BASE_DIR=""
PROCESSED_COUNT=0
FAILED_COUNT=0
SKIPPED_COUNT=0
START_TIME=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ============================================================================
# 日志和输出函数
# ============================================================================

log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [ -n "$LOG_FILE" ] && [ "$ENABLE_LOGGING" = true ]; then
        echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    fi
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log_message "INFO" "$1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    log_message "INFO" "SUCCESS: $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log_message "WARNING" "$1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log_message "ERROR" "$1"
}

print_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1"
        log_message "DEBUG" "$1"
    fi
}

# ============================================================================
# 配置加载函数
# ============================================================================

load_config() {
    # 设置默认值
    INPUT_DIR="data/encrypt_videos"
    OUTPUT_DIR="data/decrypt_videos"
    PARALLEL_COUNT=2
    FORCE_OVERWRITE=false
    VERBOSE=false
    SUPPORTED_EXTENSIONS="mp4 MP4"
    FILE_PATTERN=""
    MIN_FILE_SIZE=1024
    MAX_FILE_SIZE=0
    OUTPUT_SUFFIX="_decrypted"
    PRESERVE_STRUCTURE=true
    ERROR_BEHAVIOR="continue"
    MAX_RETRIES=1
    RETRY_INTERVAL=5
    ENABLE_LOGGING=true
    LOG_LEVEL="INFO"
    LOG_RETENTION_DAYS=7
    TEMP_DIR=""
    MEMORY_LIMIT=1024
    CHECK_DISK_SPACE=true
    MIN_FREE_SPACE=1024
    ENABLE_NOTIFICATION=false
    NOTIFICATION_METHOD="desktop"
    EMAIL_TO=""
    EMAIL_SUBJECT="海康威视视频批量解密完成"
    WEBHOOK_URL=""
    PRE_PROCESS_HOOK=""
    POST_PROCESS_HOOK=""
    FFMPEG_PARAMS="-c copy"
    VERIFY_OUTPUT=true
    GENERATE_REPORT=true
    REPORT_FILE="batch_decrypt_report.html"
    
    # 加载配置文件
    if [ -f "$CONFIG_FILE" ]; then
        print_info "加载配置文件: $CONFIG_FILE"
        source "$CONFIG_FILE"
    else
        print_warning "配置文件不存在，使用默认配置: $CONFIG_FILE"
    fi
    
    # 设置日志文件
    if [ "$ENABLE_LOGGING" = true ]; then
        LOG_FILE="$SCRIPT_DIR/${LOG_FILE:-batch_decrypt.log}"
        # 清理旧日志
        if [ "$LOG_RETENTION_DAYS" -gt 0 ]; then
            find "$SCRIPT_DIR" -name "*.log" -mtime +$LOG_RETENTION_DAYS -delete 2>/dev/null || true
        fi
    fi
    
    # 设置临时目录
    if [ -z "$TEMP_DIR" ]; then
        TEMP_BASE_DIR=$(mktemp -d)
    else
        TEMP_BASE_DIR="$TEMP_DIR"
        mkdir -p "$TEMP_BASE_DIR"
    fi
}

# ============================================================================
# 系统检查函数
# ============================================================================

check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查解密程序
    if [ ! -f "$SCRIPT_DIR/test_decrypt" ]; then
        print_warning "解密程序不存在，正在编译..."
        cd "$SCRIPT_DIR"
        if ! make all; then
            print_error "编译失败，请检查编译环境"
            exit 1
        fi
        print_success "编译完成"
    fi
    
    # 检查解密库
    if [ ! -f "$SCRIPT_DIR/lib/libhikvision_decrypt.so" ]; then
        print_error "解密库不存在，请先编译: make"
        exit 1
    fi
    
    # 检查FFmpeg
    if ! command -v ffmpeg &> /dev/null; then
        print_error "FFmpeg未安装，请安装: sudo apt install ffmpeg"
        exit 1
    fi
    
    # 检查并行处理工具
    if ! command -v parallel &> /dev/null && ! command -v xargs &> /dev/null; then
        print_error "需要GNU parallel或xargs来支持并行处理"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

check_disk_space() {
    if [ "$CHECK_DISK_SPACE" = true ]; then
        local available_mb=$(df "$OUTPUT_DIR" | awk 'NR==2 {print int($4/1024)}')
        if [ "$available_mb" -lt "$MIN_FREE_SPACE" ]; then
            print_error "磁盘空间不足: 可用 ${available_mb}MB, 需要 ${MIN_FREE_SPACE}MB"
            exit 1
        fi
        print_info "磁盘空间检查通过: ${available_mb}MB 可用"
    fi
}

# ============================================================================
# 文件处理函数
# ============================================================================

find_input_files() {
    local files=()
    
    for ext in $SUPPORTED_EXTENSIONS; do
        while IFS= read -r -d '' file; do
            # 检查文件大小
            local size=$(stat -c%s "$file")
            if [ "$size" -lt "$MIN_FILE_SIZE" ]; then
                print_debug "跳过小文件: $file (${size}字节)"
                continue
            fi
            
            if [ "$MAX_FILE_SIZE" -gt 0 ] && [ "$size" -gt "$MAX_FILE_SIZE" ]; then
                print_debug "跳过大文件: $file (${size}字节)"
                continue
            fi
            
            # 检查文件名模式
            if [ -n "$FILE_PATTERN" ]; then
                local basename=$(basename "$file")
                if [[ ! "$basename" == $FILE_PATTERN ]]; then
                    print_debug "跳过不匹配模式的文件: $file"
                    continue
                fi
            fi
            
            files+=("$file")
        done < <(find "$INPUT_DIR" -name "*.$ext" -type f -print0)
    done
    
    printf '%s\n' "${files[@]}"
}

process_single_file() {
    local input_file="$1"
    local relative_path="${input_file#$INPUT_DIR/}"
    local output_file
    
    if [ "$PRESERVE_STRUCTURE" = true ]; then
        local dir_part=$(dirname "$relative_path")
        local file_part=$(basename "$relative_path" .mp4)
        if [ "$dir_part" = "." ]; then
            output_file="$OUTPUT_DIR/${file_part}${OUTPUT_SUFFIX}.mp4"
        else
            output_file="$OUTPUT_DIR/$dir_part/${file_part}${OUTPUT_SUFFIX}.mp4"
        fi
    else
        local file_part=$(basename "$relative_path" .mp4)
        output_file="$OUTPUT_DIR/${file_part}${OUTPUT_SUFFIX}.mp4"
    fi
    
    local output_dir=$(dirname "$output_file")
    mkdir -p "$output_dir"
    
    # 检查是否需要跳过
    if [ -f "$output_file" ] && [ "$FORCE_OVERWRITE" = false ]; then
        print_warning "跳过已存在的文件: $(basename "$output_file")"
        ((SKIPPED_COUNT++))
        return 0
    fi
    
    print_info "处理文件: $relative_path"
    
    # 执行预处理钩子
    if [ -n "$PRE_PROCESS_HOOK" ] && [ -x "$PRE_PROCESS_HOOK" ]; then
        "$PRE_PROCESS_HOOK" "$input_file" "$output_file"
    fi
    
    local retry_count=0
    local success=false
    
    while [ $retry_count -le $MAX_RETRIES ] && [ "$success" = false ]; do
        if [ $retry_count -gt 0 ]; then
            print_info "重试 $retry_count/$MAX_RETRIES: $relative_path"
            sleep $RETRY_INTERVAL
        fi
        
        # 创建临时工作目录
        local temp_dir=$(mktemp -d -p "$TEMP_BASE_DIR")
        local temp_input="$temp_dir/input.mp4"
        local temp_output="$temp_dir/output.mp4"
        
        # 复制输入文件到临时目录
        if cp "$input_file" "$temp_input"; then
            # 在临时目录中执行解密
            (
                cd "$temp_dir"
                cp "$SCRIPT_DIR/test_decrypt" .
                cp -r "$SCRIPT_DIR/lib" .
                cp -r "$SCRIPT_DIR/include" .
                mkdir -p videos
                cp "$temp_input" "videos/encrpyted_video_1.mp4"
                
                # 设置库路径并执行解密
                export LD_LIBRARY_PATH="./lib:$LD_LIBRARY_PATH"
                
                if [ "$VERBOSE" = true ]; then
                    timeout 300 ./test_decrypt
                else
                    timeout 300 ./test_decrypt > /dev/null 2>&1
                fi
                
                # 检查解密结果
                if [ -f "hikvision_decrypted.mp4" ]; then
                    cp "hikvision_decrypted.mp4" "$temp_output"
                else
                    exit 1
                fi
            )
            
            # 检查处理结果
            if [ $? -eq 0 ] && [ -f "$temp_output" ]; then
                # 验证输出文件
                if [ "$VERIFY_OUTPUT" = true ]; then
                    if ffprobe -v quiet "$temp_output" 2>/dev/null; then
                        mv "$temp_output" "$output_file"
                        success=true
                        print_success "完成: $relative_path -> $(basename "$output_file")"
                        ((PROCESSED_COUNT++))
                        
                        # 显示文件大小信息
                        if [ "$VERBOSE" = true ]; then
                            local input_size=$(stat -c%s "$input_file")
                            local output_size=$(stat -c%s "$output_file")
                            print_info "  输入: $(numfmt --to=iec-i --suffix=B $input_size)"
                            print_info "  输出: $(numfmt --to=iec-i --suffix=B $output_size)"
                        fi
                    else
                        print_error "输出文件验证失败: $relative_path"
                    fi
                else
                    mv "$temp_output" "$output_file"
                    success=true
                    print_success "完成: $relative_path -> $(basename "$output_file")"
                    ((PROCESSED_COUNT++))
                fi
            fi
        fi
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        if [ "$success" = false ]; then
            ((retry_count++))
        fi
    done
    
    if [ "$success" = false ]; then
        print_error "处理失败: $relative_path"
        ((FAILED_COUNT++))
        
        if [ "$ERROR_BEHAVIOR" = "stop" ]; then
            exit 1
        fi
    fi
    
    # 执行后处理钩子
    if [ -n "$POST_PROCESS_HOOK" ] && [ -x "$POST_PROCESS_HOOK" ]; then
        "$POST_PROCESS_HOOK" "$input_file" "$output_file" "$success"
    fi
    
    return $([ "$success" = true ] && echo 0 || echo 1)
}

# ============================================================================
# 主处理函数
# ============================================================================

main() {
    print_info "=== 海康威视视频批量解密工具 (增强版) ==="
    
    # 加载配置
    load_config
    
    print_info "输入目录: $INPUT_DIR"
    print_info "输出目录: $OUTPUT_DIR"
    print_info "并行数量: $PARALLEL_COUNT"
    print_info "强制覆盖: $FORCE_OVERWRITE"
    
    # 检查输入目录
    if [ ! -d "$INPUT_DIR" ]; then
        print_error "输入目录不存在: $INPUT_DIR"
        exit 1
    fi
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 系统检查
    check_dependencies
    check_disk_space
    
    # 查找输入文件
    print_info "扫描输入目录中的文件..."
    mapfile -t input_files < <(find_input_files)
    
    if [ ${#input_files[@]} -eq 0 ]; then
        print_warning "在输入目录中未找到符合条件的文件"
        exit 0
    fi
    
    print_info "找到 ${#input_files[@]} 个文件待处理"
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 导出函数和变量以供并行使用
    export -f process_single_file print_info print_success print_warning print_error print_debug log_message
    export INPUT_DIR OUTPUT_DIR FORCE_OVERWRITE VERBOSE PRESERVE_STRUCTURE OUTPUT_SUFFIX
    export ERROR_BEHAVIOR MAX_RETRIES RETRY_INTERVAL VERIFY_OUTPUT PRE_PROCESS_HOOK POST_PROCESS_HOOK
    export SCRIPT_DIR TEMP_BASE_DIR LOG_FILE ENABLE_LOGGING
    export RED GREEN YELLOW BLUE PURPLE CYAN NC
    
    # 开始批量处理
    print_info "开始批量处理 (并行数: $PARALLEL_COUNT)..."
    
    # 使用GNU parallel或xargs进行并行处理
    if command -v parallel &> /dev/null; then
        printf '%s\n' "${input_files[@]}" | parallel -j "$PARALLEL_COUNT" process_single_file
    else
        printf '%s\n' "${input_files[@]}" | xargs -I {} -P "$PARALLEL_COUNT" bash -c 'process_single_file "$@"' _ {}
    fi
    
    # 计算处理时间
    local end_time=$(date +%s)
    local duration=$((end_time - START_TIME))
    
    print_success "=== 批量处理完成 ==="
    print_info "处理时间: ${duration}秒"
    print_info "输出目录: $OUTPUT_DIR"
    
    # 统计结果
    local total_files=${#input_files[@]}
    
    print_info "处理统计:"
    print_info "  总文件数: $total_files"
    print_info "  成功: $PROCESSED_COUNT"
    print_info "  失败: $FAILED_COUNT"
    print_info "  跳过: $SKIPPED_COUNT"
    
    # 生成报告
    if [ "$GENERATE_REPORT" = true ]; then
        generate_report "$total_files" "$duration"
    fi
    
    # 发送通知
    if [ "$ENABLE_NOTIFICATION" = true ]; then
        send_notification "$total_files" "$duration"
    fi
    
    # 清理临时目录
    rm -rf "$TEMP_BASE_DIR"
    
    if [ $FAILED_COUNT -gt 0 ]; then
        print_warning "有 $FAILED_COUNT 个文件处理失败"
        exit 1
    else
        print_success "所有文件处理成功！"
    fi
}

# ============================================================================
# 报告生成函数
# ============================================================================

generate_report() {
    local total_files="$1"
    local duration="$2"
    local report_path="$SCRIPT_DIR/$REPORT_FILE"

    print_info "生成处理报告: $report_path"

    cat > "$report_path" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海康威视视频批量解密报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid #007bff; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .success { border-left-color: #28a745; } .success .stat-number { color: #28a745; }
        .danger { border-left-color: #dc3545; } .danger .stat-number { color: #dc3545; }
        .warning { border-left-color: #ffc107; } .warning .stat-number { color: #ffc107; }
        .info-section { margin: 20px 0; }
        .info-title { font-size: 1.2em; font-weight: bold; color: #333; margin-bottom: 10px; }
        .info-content { background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .footer { text-align: center; color: #666; font-size: 0.9em; margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>海康威视视频批量解密报告</h1>
            <p>生成时间: $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>

        <div class="summary">
            <div class="stat-card">
                <div class="stat-number">$total_files</div>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">$PROCESSED_COUNT</div>
                <div class="stat-label">成功处理</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">$FAILED_COUNT</div>
                <div class="stat-label">处理失败</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">$SKIPPED_COUNT</div>
                <div class="stat-label">跳过文件</div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">处理配置</div>
            <div class="info-content">
                <p><strong>输入目录:</strong> $INPUT_DIR</p>
                <p><strong>输出目录:</strong> $OUTPUT_DIR</p>
                <p><strong>并行数量:</strong> $PARALLEL_COUNT</p>
                <p><strong>处理时间:</strong> ${duration}秒</p>
                <p><strong>平均处理速度:</strong> $(echo "scale=2; $total_files / $duration" | bc 2>/dev/null || echo "N/A") 文件/秒</p>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">系统信息</div>
            <div class="info-content">
                <p><strong>主机名:</strong> $(hostname)</p>
                <p><strong>操作系统:</strong> $(uname -s) $(uname -r)</p>
                <p><strong>CPU核心数:</strong> $(nproc)</p>
                <p><strong>内存使用:</strong> $(free -h | awk 'NR==2{printf "%.1f/%.1f GB (%.1f%%)", \$3/1024/1024, \$2/1024/1024, \$3*100/\$2}')</p>
                <p><strong>磁盘使用:</strong> $(df -h "$OUTPUT_DIR" | awk 'NR==2{printf "%s/%s (%s)", \$3, \$2, \$5}')</p>
            </div>
        </div>

        <div class="footer">
            <p>海康威视视频批量解密工具 - 增强版</p>
            <p>报告文件: $report_path</p>
        </div>
    </div>
</body>
</html>
EOF

    print_success "报告生成完成: $report_path"
}

# ============================================================================
# 通知发送函数
# ============================================================================

send_notification() {
    local total_files="$1"
    local duration="$2"

    local message="海康威视视频批量解密完成
总文件数: $total_files
成功: $PROCESSED_COUNT
失败: $FAILED_COUNT
跳过: $SKIPPED_COUNT
处理时间: ${duration}秒"

    case "$NOTIFICATION_METHOD" in
        "desktop")
            if command -v notify-send &> /dev/null; then
                notify-send "批量解密完成" "$message"
                print_info "桌面通知已发送"
            fi
            ;;
        "email")
            if [ -n "$EMAIL_TO" ] && command -v mail &> /dev/null; then
                echo "$message" | mail -s "$EMAIL_SUBJECT" "$EMAIL_TO"
                print_info "邮件通知已发送到: $EMAIL_TO"
            fi
            ;;
        "webhook")
            if [ -n "$WEBHOOK_URL" ] && command -v curl &> /dev/null; then
                curl -X POST -H "Content-Type: application/json" \
                     -d "{\"text\":\"$message\"}" \
                     "$WEBHOOK_URL" &> /dev/null
                print_info "Webhook通知已发送"
            fi
            ;;
    esac
}

# ============================================================================
# 显示使用说明
# ============================================================================

show_usage() {
    echo "海康威视视频批量解密工具 - 增强版"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE   指定配置文件 (默认: batch_config.conf)"
    echo "  -i, --input DIR     输入目录 (覆盖配置文件设置)"
    echo "  -o, --output DIR    输出目录 (覆盖配置文件设置)"
    echo "  -p, --parallel N    并行处理数量 (覆盖配置文件设置)"
    echo "  -f, --force         强制覆盖已存在的文件"
    echo "  -v, --verbose       详细输出"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -c my_config.conf                 # 使用自定义配置文件"
    echo "  $0 -i /path/to/input -o /path/to/output"
    echo "  $0 -p 4 -f -v                        # 4个并行进程，强制覆盖，详细输出"
    echo ""
    echo "配置文件:"
    echo "  配置文件包含所有可调整的参数，包括目录路径、处理选项、"
    echo "  日志设置、通知配置等。详细说明请参考 batch_config.conf"
    echo ""
}

# ============================================================================
# 命令行参数解析
# ============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -i|--input)
                INPUT_DIR="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -p|--parallel)
                PARALLEL_COUNT="$2"
                shift 2
                ;;
            -f|--force)
                FORCE_OVERWRITE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 运行主函数
parse_arguments "$@"
main
